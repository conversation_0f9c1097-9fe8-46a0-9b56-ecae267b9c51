import 'dart:convert';
import 'dart:io';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;

import 'package:diogeneschatbot/features/image_generation/models/image_generation_settings.dart';
import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/util/util.dart';
import 'package:diogeneschatbot/util/util_api_usage.dart';
import 'package:diogeneschatbot/utils/logger.dart';

/// Service for direct OpenAI API image operations
///
/// Handles all direct communication with OpenAI's image generation,
/// editing, and variation APIs. Includes proper error handling,
/// usage tracking, and modern DALL-E 3 features.
class OpenAIImageService {
  // Private constructor to prevent instantiation
  OpenAIImageService._();

  /// Generates an image directly using OpenAI API with enhanced settings
  ///
  /// Calls OpenAI's DALL-E API directly with proper style integration and
  /// modern features like quality settings and aspect ratio support.
  ///
  /// [prompt] The enhanced prompt for image generation
  /// [settings] Image generation settings including style and parameters
  /// [userId] Current user ID for tracking
  /// [usageType] Usage type for limit checking
  ///
  /// Returns the Firebase URL of the generated image
  static Future<String> generateImage(
    String prompt,
    ImageGenerationSettings settings,
    String userId,
    Usage usageType,
  ) async {
    // Check usage limit before making API call
    if (await Util_API_Usage.isLimitExceeded(usageType, userId)) {
      throw Exception('Usage limit (daily, monthly, specific API\'s) exceeded');
    }

    final String openaiApiKey = dotenv.env['OPENAI_API_KEY'] ??
        const String.fromEnvironment("OPENAI_API_KEY");

    if (openaiApiKey.isEmpty) {
      throw Exception('OpenAI API key not configured');
    }

    // Prepare headers
    final headers = {
      "Content-Type": "application/json",
      "Authorization": "Bearer $openaiApiKey",
    };

    // Determine image size based on aspect ratio and settings
    String imageSize = _getImageSize(settings.aspectRatio, settings.imageSize);

    // Build request body with modern DALL-E 3 features
    final requestBody = {
      "model": "dall-e-3", // Use DALL-E 3 for better quality
      "prompt": prompt,
      "n": 1, // DALL-E 3 only supports n=1
      "size": imageSize,
      "quality": "hd", // Use HD quality for better results
      "style": _getOpenAIStyle(settings), // Map our style to OpenAI's style parameter
      "response_format": "b64_json",
      "user": userId,
    };

    logger.d('OpenAI request: ${json.encode(requestBody)}');

    try {
      final response = await http.post(
        Uri.parse('https://api.openai.com/v1/images/generations'),
        headers: headers,
        body: json.encode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final b64ImageData = responseData['data'][0]['b64_json'];

        // Save to Firebase Storage and return URL
        String firebaseUrl = await Util.SaveBase64ToFirebaseStorage(
          b64ImageData,
          userId,
        );

        logger.d('Image generated successfully: $firebaseUrl');
        return firebaseUrl;
      } else {
        final errorData = json.decode(response.body);
        final errorMessage = errorData['error']?['message'] ?? 'Unknown error';
        throw Exception('OpenAI API error (${response.statusCode}): $errorMessage');
      }
    } catch (e) {
      logger.e('Error generating image: $e');
      throw Exception('Failed to generate image: $e');
    }
  }

  /// Edits an image directly using OpenAI API
  ///
  /// Calls OpenAI's image edit endpoint with the provided image, mask, and prompt.
  ///
  /// [image] The source image to edit
  /// [mask] Optional mask image for selective editing
  /// [prompt] The editing instruction
  /// [userId] Current user ID for tracking
  /// [usageType] Usage type for limit checking
  ///
  /// Returns the Firebase URL of the edited image
  static Future<String> editImage(
    File image,
    File? mask,
    String prompt,
    String userId,
    Usage usageType,
  ) async {
    // Check usage limit before making API call
    if (await Util_API_Usage.isLimitExceeded(usageType, userId)) {
      throw Exception('Usage limit (daily, monthly, specific API\'s) exceeded');
    }

    final String openaiApiKey = dotenv.env['OPENAI_API_KEY'] ??
        const String.fromEnvironment("OPENAI_API_KEY");

    if (openaiApiKey.isEmpty) {
      throw Exception('OpenAI API key not configured');
    }

    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('https://api.openai.com/v1/images/edits'),
      );

      request.headers['Authorization'] = 'Bearer $openaiApiKey';

      // Add form fields
      request.fields['prompt'] = prompt;
      request.fields['n'] = '1';
      request.fields['size'] = '1024x1024';
      request.fields['response_format'] = 'b64_json';
      request.fields['user'] = userId;

      // Add image file
      final imageBytes = await image.readAsBytes();
      request.files.add(http.MultipartFile.fromBytes(
        'image',
        imageBytes,
        filename: 'image.png',
      ));

      // Add mask file if provided
      if (mask != null) {
        final maskBytes = await mask.readAsBytes();
        request.files.add(http.MultipartFile.fromBytes(
          'mask',
          maskBytes,
          filename: 'mask.png',
        ));
      }

      final response = await http.Response.fromStream(await request.send());

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final b64ImageData = responseData['data'][0]['b64_json'];

        // Save to Firebase Storage and return URL
        String firebaseUrl = await Util.SaveBase64ToFirebaseStorage(
          b64ImageData,
          userId,
        );

        logger.d('Image edited successfully: $firebaseUrl');
        return firebaseUrl;
      } else {
        final errorData = json.decode(response.body);
        final errorMessage = errorData['error']?['message'] ?? 'Unknown error';
        throw Exception('OpenAI API error (${response.statusCode}): $errorMessage');
      }
    } catch (e) {
      logger.e('Error editing image: $e');
      throw Exception('Failed to edit image: $e');
    }
  }

  /// Creates variations of an image directly using OpenAI API
  ///
  /// Calls OpenAI's image variations endpoint with the provided image.
  ///
  /// [image] The source image to create variations from
  /// [userId] Current user ID for tracking
  /// [usageType] Usage type for limit checking
  ///
  /// Returns the Firebase URL of the variation image
  static Future<String> createVariations(
    File image,
    String userId,
    Usage usageType,
  ) async {
    // Check usage limit before making API call
    if (await Util_API_Usage.isLimitExceeded(usageType, userId)) {
      throw Exception('Usage limit (daily, monthly, specific API\'s) exceeded');
    }

    final String openaiApiKey = dotenv.env['OPENAI_API_KEY'] ??
        const String.fromEnvironment("OPENAI_API_KEY");

    if (openaiApiKey.isEmpty) {
      throw Exception('OpenAI API key not configured');
    }

    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('https://api.openai.com/v1/images/variations'),
      );

      request.headers['Authorization'] = 'Bearer $openaiApiKey';

      // Add form fields
      request.fields['n'] = '1';
      request.fields['size'] = '1024x1024';
      request.fields['response_format'] = 'b64_json';
      request.fields['user'] = userId;

      // Add image file
      final imageBytes = await image.readAsBytes();
      request.files.add(http.MultipartFile.fromBytes(
        'image',
        imageBytes,
        filename: 'image.png',
      ));

      final response = await http.Response.fromStream(await request.send());

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final b64ImageData = responseData['data'][0]['b64_json'];

        // Save to Firebase Storage and return URL
        String firebaseUrl = await Util.SaveBase64ToFirebaseStorage(
          b64ImageData,
          userId,
        );

        logger.d('Image variation created successfully: $firebaseUrl');
        return firebaseUrl;
      } else {
        final errorData = json.decode(response.body);
        final errorMessage = errorData['error']?['message'] ?? 'Unknown error';
        throw Exception('OpenAI API error (${response.statusCode}): $errorMessage');
      }
    } catch (e) {
      logger.e('Error creating image variation: $e');
      throw Exception('Failed to create image variation: $e');
    }
  }

  /// Maps image generation settings to OpenAI's native style parameter
  static String _getOpenAIStyle(ImageGenerationSettings settings) {
    // OpenAI DALL-E 3 supports "vivid" and "natural" styles
    if (settings.styleCategory == StyleCategory.photorealistic ||
        settings.selectedStyle?.toLowerCase().contains('photography') == true) {
      return "natural";
    }
    return "vivid"; // Default to vivid for artistic and creative styles
  }

  /// Determines the appropriate image size based on aspect ratio and settings
  static String _getImageSize(double aspectRatio, int imageSize) {
    // DALL-E 3 supports: 1024x1024, 1024x1792, 1792x1024
    if (aspectRatio > 1.2) {
      return "1792x1024"; // Landscape
    } else if (aspectRatio < 0.8) {
      return "1024x1792"; // Portrait
    } else {
      return "1024x1024"; // Square
    }
  }
}
