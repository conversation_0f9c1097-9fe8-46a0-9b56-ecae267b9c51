import 'package:flutter/material.dart';

import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';

/// Empty state widget for the image generation page
///
/// Displays helpful information, tips, and feature highlights when no images
/// have been generated yet. Provides an engaging onboarding experience.
class ImageGenerationEmptyState extends StatelessWidget {
  /// Callback when user wants to start creating
  final VoidCallback? onStartCreating;

  const ImageGenerationEmptyState({
    super.key,
    this.onStartCreating,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Main welcome card
          _buildWelcomeCard(context, theme),

          // Tips and examples section
          _buildTipsSection(theme),

          // Feature highlights
          _buildFeatureHighlights(theme),
        ],
      ),
    );
  }

  /// Builds the main welcome card with animated icon
  Widget _buildWelcomeCard(BuildContext context, ThemeData theme) {
    return CardStyles.outlined(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Animated icon with glow effect
          TweenAnimationBuilder<double>(
            duration: const Duration(seconds: 2),
            tween: Tween(begin: 0.5, end: 1.0),
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.primaryGreen.withValues(alpha: 0.3),
                        blurRadius: 20 * value,
                        spreadRadius: 5 * value,
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.auto_awesome,
                    size: 64,
                    color: AppTheme.primaryGreen,
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 16),
          Text(
            'AI Image Generator',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: AppTheme.primaryGreen,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Transform your imagination into stunning visuals with AI-powered image generation.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.hintColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: onStartCreating,
            icon: const Icon(Icons.create, color: Colors.white),
            label: const Text(
              'Start Creating',
              style: TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds helpful tips section for users
  Widget _buildTipsSection(ThemeData theme) {
    final tips = [
      'Use Style Presets: Choose from photorealistic, artistic, technical, or creative styles',
      'Try Advanced Settings: Add lighting moods, composition guides, and subject focus',
      'Enable Enhanced Prompting: Let AI automatically improve your prompts',
      'Be specific: "A red sports car on a mountain road at sunset"',
      'Combine elements: Style + lighting + composition for best results',
    ];

    return CardStyles.outlined(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb_outline, color: AppTheme.secondaryGreen),
              const SizedBox(width: 8),
              Text(
                'Pro Tips for Better Results',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.secondaryGreen,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...tips.map(
            (tip) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 16,
                    color: AppTheme.primaryGreen,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      tip,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.hintColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds feature highlights section
  Widget _buildFeatureHighlights(ThemeData theme) {
    final features = [
      {
        'icon': Icons.palette,
        'title': 'Style Presets',
        'description': 'Professional style library with 30+ presets',
      },
      {
        'icon': Icons.tune,
        'title': 'Advanced Controls',
        'description': 'Lighting, composition, and subject templates',
      },
      {
        'icon': Icons.auto_awesome,
        'title': 'Enhanced Prompting',
        'description': 'AI-powered prompt optimization',
      },
      {
        'icon': Icons.edit,
        'title': 'Image Editing',
        'description': 'Upload and edit existing images with AI',
      },
      {
        'icon': Icons.auto_fix_high,
        'title': 'Variations',
        'description': 'Create multiple versions of your images',
      },
      {
        'icon': Icons.offline_bolt,
        'title': 'Offline Mode',
        'description': 'Generate images locally on supported devices',
      },
    ];

    return CardStyles.outlined(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Features',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryGreen,
            ),
          ),
          const SizedBox(height: 12),
          ...features.map(
            (feature) => Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.cardColor.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    feature['icon'] as IconData,
                    color: AppTheme.primaryGreen,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          feature['title'] as String,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          feature['description'] as String,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.hintColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
