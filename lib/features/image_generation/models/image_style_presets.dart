import 'package:diogeneschatbot/features/image_generation/models/image_generation_settings.dart';
import 'package:diogeneschatbot/utils/logger.dart';

/// Style presets and constants for enhanced image generation
///
/// Provides predefined styles, lighting descriptions, and prompt building utilities
/// for creating high-quality AI-generated images.
class ImageStylePresets {
  // Private constructor to prevent instantiation
  ImageStylePresets._();

  /// Predefined styles organized by category
  static const Map<StyleCategory, List<String>> stylesByCategory = {
    StyleCategory.photorealistic: [
      'Photography',
      'Portrait Photography',
      'Landscape Photography',
      'Street Photography',
      'Documentary Style',
      'Fashion Photography',
      'Product Photography',
    ],
    StyleCategory.artistic: [
      'Oil Painting',
      'Watercolor',
      'Digital Art',
      'Impressionism',
      'Surrealism',
      'Cubism',
      'Abstract Art',
      'Pop Art',
      'Minimalism',
    ],
    StyleCategory.technical: [
      'Technical Drawing',
      'Blueprint',
      'Architectural Rendering',
      'Scientific Illustration',
      'Infographic Style',
      'Diagram',
    ],
    StyleCategory.creative: [
      'Cartoon',
      'Anime',
      'Vector Art',
      'Pixel Art',
      'Graffiti',
      'Comic Book',
      'Fantasy Art',
      'Steampunk',
      'Cyberpunk',
      'Retro',
      'Vintage',
    ],
  };

  static const Map<LightingMood, String> lightingDescriptions = {
    LightingMood.dramatic: 'dramatic lighting, high contrast, deep shadows',
    LightingMood.soft: 'soft lighting, diffused light, gentle shadows',
    LightingMood.neon: 'neon lighting, vibrant colors, electric atmosphere',
    LightingMood.vintage: 'vintage lighting, warm tones, nostalgic mood',
    LightingMood.natural: 'natural lighting, daylight, realistic illumination',
    LightingMood.cinematic: 'cinematic lighting, film-like quality, professional',
    LightingMood.golden: 'golden hour lighting, warm glow, sunset ambiance',
    LightingMood.blue: 'blue hour lighting, cool tones, twilight atmosphere',
  };

  static const Map<CompositionGuide, String> compositionDescriptions = {
    CompositionGuide.ruleOfThirds: 'rule of thirds composition, balanced framing',
    CompositionGuide.goldenRatio: 'golden ratio composition, harmonious proportions',
    CompositionGuide.centered: 'centered composition, symmetrical balance',
    CompositionGuide.leadingLines: 'leading lines composition, directional flow',
    CompositionGuide.symmetrical: 'symmetrical composition, mirror balance',
    CompositionGuide.asymmetrical: 'asymmetrical composition, dynamic balance',
  };

  static const Map<SubjectTemplate, String> subjectPrompts = {
    SubjectTemplate.portrait: 'portrait, face focus, detailed features, professional headshot',
    SubjectTemplate.landscape: 'landscape, wide view, natural scenery, environmental',
    SubjectTemplate.product: 'product photography, clean background, commercial style',
    SubjectTemplate.logo: 'logo design, simple, memorable, brand identity',
    SubjectTemplate.architecture: 'architectural, building design, structural details',
    SubjectTemplate.food: 'food photography, appetizing, detailed textures',
    SubjectTemplate.fashion: 'fashion photography, stylish, trendy, clothing focus',
    SubjectTemplate.abstract: 'abstract art, conceptual, non-representational',
  };

  /// Builds an enhanced prompt by combining base prompt with style settings
  ///
  /// Takes a base prompt and applies various style enhancements based on the
  /// provided settings. Returns a comprehensive prompt for AI image generation.
  ///
  /// [basePrompt] The original user prompt
  /// [settings] Configuration settings for style enhancement
  ///
  /// Returns enhanced prompt string ready for AI generation
  static String buildEnhancedPrompt(String basePrompt, ImageGenerationSettings settings) {
    if (basePrompt.trim().isEmpty) {
      throw ArgumentError('Base prompt cannot be empty');
    }

    final List<String> promptParts = [basePrompt.trim()];

    // Add style if selected - prioritize specific style over category
    if (settings.selectedStyle != null && settings.selectedStyle!.isNotEmpty) {
      promptParts.add('in ${settings.selectedStyle!.toLowerCase()} style');
    } else if (settings.styleCategory != null) {
      // Add general category style if no specific style selected
      switch (settings.styleCategory!) {
        case StyleCategory.photorealistic:
          promptParts.add('photorealistic, realistic photography');
          break;
        case StyleCategory.artistic:
          promptParts.add('artistic, creative art style');
          break;
        case StyleCategory.technical:
          promptParts.add('technical illustration, precise details');
          break;
        case StyleCategory.creative:
          promptParts.add('creative, imaginative art style');
          break;
      }
    }

    // Add lighting mood description
    if (settings.lightingMood != null) {
      final lightingDesc = lightingDescriptions[settings.lightingMood!];
      if (lightingDesc != null) {
        promptParts.add(lightingDesc);
      }
    }

    // Add composition guide
    if (settings.compositionGuide != null) {
      final compositionDesc = compositionDescriptions[settings.compositionGuide!];
      if (compositionDesc != null) {
        promptParts.add(compositionDesc);
      }
    }

    // Add subject template
    if (settings.subjectTemplate != null) {
      final subjectDesc = subjectPrompts[settings.subjectTemplate!];
      if (subjectDesc != null) {
        promptParts.add(subjectDesc);
      }
    }

    // Add negative prompt handling
    if (settings.negativePrompt.isNotEmpty) {
      promptParts.add('avoiding: ${settings.negativePrompt}');
    }

    // Add quality enhancers for better results (only if advanced prompting is enabled)
    if (settings.useAdvancedPrompting) {
      promptParts.addAll(const [
        'high quality',
        'detailed',
        'professional',
        '8k resolution',
        'masterpiece',
      ]);
    }

    final enhancedPrompt = promptParts.join(', ');

    // Log the enhancement for debugging
    logger.d('Original prompt: $basePrompt');
    logger.d('Enhanced prompt: $enhancedPrompt');

    return enhancedPrompt;
  }

  /// Gets all available styles for a given category
  static List<String> getStylesForCategory(StyleCategory category) {
    return stylesByCategory[category] ?? [];
  }

  /// Gets a formatted display name for a style category
  static String getCategoryDisplayName(StyleCategory category) {
    switch (category) {
      case StyleCategory.photorealistic:
        return 'Photorealistic';
      case StyleCategory.artistic:
        return 'Artistic';
      case StyleCategory.technical:
        return 'Technical';
      case StyleCategory.creative:
        return 'Creative';
    }
  }

  /// Gets a formatted display name for a lighting mood
  static String getLightingDisplayName(LightingMood mood) {
    switch (mood) {
      case LightingMood.dramatic:
        return 'Dramatic';
      case LightingMood.soft:
        return 'Soft';
      case LightingMood.neon:
        return 'Neon';
      case LightingMood.vintage:
        return 'Vintage';
      case LightingMood.natural:
        return 'Natural';
      case LightingMood.cinematic:
        return 'Cinematic';
      case LightingMood.golden:
        return 'Golden Hour';
      case LightingMood.blue:
        return 'Blue Hour';
    }
  }

  /// Gets a formatted display name for a composition guide
  static String getCompositionDisplayName(CompositionGuide guide) {
    switch (guide) {
      case CompositionGuide.ruleOfThirds:
        return 'Rule of Thirds';
      case CompositionGuide.goldenRatio:
        return 'Golden Ratio';
      case CompositionGuide.centered:
        return 'Centered';
      case CompositionGuide.leadingLines:
        return 'Leading Lines';
      case CompositionGuide.symmetrical:
        return 'Symmetrical';
      case CompositionGuide.asymmetrical:
        return 'Asymmetrical';
    }
  }

  /// Gets a formatted display name for a subject template
  static String getSubjectDisplayName(SubjectTemplate template) {
    switch (template) {
      case SubjectTemplate.portrait:
        return 'Portrait';
      case SubjectTemplate.landscape:
        return 'Landscape';
      case SubjectTemplate.product:
        return 'Product';
      case SubjectTemplate.logo:
        return 'Logo';
      case SubjectTemplate.architecture:
        return 'Architecture';
      case SubjectTemplate.food:
        return 'Food';
      case SubjectTemplate.fashion:
        return 'Fashion';
      case SubjectTemplate.abstract:
        return 'Abstract';
    }
  }
}
