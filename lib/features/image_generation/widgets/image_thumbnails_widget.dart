import 'dart:io';

import 'package:flutter/material.dart';

import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';

/// Widget for displaying selected image thumbnails
///
/// Shows thumbnails of selected source and mask images for editing
/// and variation operations. Provides clear visual feedback and
/// easy removal options.
class ImageThumbnailsWidget extends StatelessWidget {
  /// The selected source image for editing or variations
  final File? sourceImage;

  /// The selected mask image for selective editing
  final File? maskImage;

  /// Callback when user wants to clear all images
  final VoidCallback? onClearImages;

  /// Callback when user wants to remove the source image
  final VoidCallback? onRemoveSourceImage;

  /// Callback when user wants to remove the mask image
  final VoidCallback? onRemoveMaskImage;

  const ImageThumbnailsWidget({
    super.key,
    this.sourceImage,
    this.maskImage,
    this.onClearImages,
    this.onRemoveSourceImage,
    this.onRemoveMaskImage,
  });

  @override
  Widget build(BuildContext context) {
    // Don't show widget if no images are selected
    if (sourceImage == null && maskImage == null) {
      return const SizedBox.shrink();
    }

    return CardStyles.outlined(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 8),
          _buildImageThumbnails(),
        ],
      ),
    );
  }

  /// Builds the header with title and clear button
  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.image, color: AppTheme.primaryGreen, size: 20),
        const SizedBox(width: 8),
        Text(
          'Selected Images',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryGreen,
          ),
        ),
        const Spacer(),
        if (onClearImages != null)
          IconButton(
            icon: const Icon(Icons.clear, size: 18),
            onPressed: onClearImages,
            tooltip: 'Clear all images',
            style: IconButton.styleFrom(
              padding: const EdgeInsets.all(4),
              minimumSize: const Size(24, 24),
            ),
          ),
      ],
    );
  }

  /// Builds the row of image thumbnails
  Widget _buildImageThumbnails() {
    return Row(
      children: [
        // Source image thumbnail
        if (sourceImage != null) ...[
          _buildImageThumbnail(
            image: sourceImage!,
            label: 'Source Image',
            labelColor: AppTheme.primaryGreen,
            onRemove: onRemoveSourceImage,
          ),
          if (maskImage != null) const SizedBox(width: 16),
        ],

        // Mask image thumbnail
        if (maskImage != null) ...[
          _buildImageThumbnail(
            image: maskImage!,
            label: 'Mask Image',
            labelColor: AppTheme.secondaryGreen,
            onRemove: onRemoveMaskImage,
          ),
        ],
      ],
    );
  }

  /// Builds a single image thumbnail with label and remove option
  Widget _buildImageThumbnail({
    required File image,
    required String label,
    required Color labelColor,
    VoidCallback? onRemove,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Thumbnail container with shadow
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Stack(
            children: [
              // Image thumbnail
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.file(
                  image,
                  width: 60,
                  height: 60,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.broken_image,
                        color: Colors.grey[600],
                        size: 24,
                      ),
                    );
                  },
                ),
              ),

              // Remove button overlay
              if (onRemove != null)
                Positioned(
                  top: 2,
                  right: 2,
                  child: GestureDetector(
                    onTap: onRemove,
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.8),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 12,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),

        const SizedBox(width: 8),

        // Label
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: labelColor,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              _getImageInfo(image),
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Gets basic image information for display
  String _getImageInfo(File image) {
    try {
      final stats = image.statSync();
      final sizeInKB = (stats.size / 1024).round();
      return '${sizeInKB}KB';
    } catch (e) {
      return 'Unknown size';
    }
  }
}

/// Helper widget for image selection buttons
class ImageSelectionButton extends StatelessWidget {
  /// The icon to display
  final IconData icon;

  /// The button label
  final String label;

  /// The button tooltip
  final String tooltip;

  /// Callback when button is pressed
  final VoidCallback? onPressed;

  /// Whether the button is enabled
  final bool enabled;

  const ImageSelectionButton({
    super.key,
    required this.icon,
    required this.label,
    required this.tooltip,
    this.onPressed,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip,
      child: ElevatedButton.icon(
        onPressed: enabled ? onPressed : null,
        icon: Icon(icon, size: 18),
        label: Text(label),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryGreen,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
}

/// Helper widget for image operation buttons
class ImageOperationButton extends StatelessWidget {
  /// The operation icon
  final IconData icon;

  /// The operation label
  final String label;

  /// The operation description
  final String description;

  /// Callback when button is pressed
  final VoidCallback? onPressed;

  /// Whether the button is enabled
  final bool enabled;

  const ImageOperationButton({
    super.key,
    required this.icon,
    required this.label,
    required this.description,
    this.onPressed,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: InkWell(
        onTap: enabled ? onPressed : null,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              Icon(
                icon,
                color: enabled ? AppTheme.primaryGreen : Colors.grey,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      label,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: enabled ? null : Colors.grey,
                      ),
                    ),
                    Text(
                      description,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: enabled ? theme.hintColor : Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: enabled ? Colors.grey : Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
