import 'dart:async';
import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

import 'package:diogeneschatbot/features/image_generation/models/image_generation_settings.dart';
import 'package:diogeneschatbot/features/image_generation/services/image_generation_service.dart';
import 'package:diogeneschatbot/features/image_generation/widgets/empty_state_widget.dart';
import 'package:diogeneschatbot/features/image_generation/widgets/image_generation_input_area.dart';
import 'package:diogeneschatbot/features/image_generation/widgets/image_generation_settings_panel.dart';
import 'package:diogeneschatbot/features/image_generation/widgets/image_style_presets_panel.dart';
import 'package:diogeneschatbot/features/image_generation/widgets/image_thumbnails_widget.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/view.dart';
import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/pages/chathistory_page.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/util/util_token.dart';
import 'package:diogeneschatbot/utils/image_utils.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/widgets/chat_message_widgets.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';

// TODO: Add image gallery widget for better image browsing experience
// TODO: Implement image favorites/bookmarking system
// TODO: Add image metadata display (size, format, generation time)
// TODO: Implement image sharing functionality
// TODO: Add batch processing for multiple images
// TODO: Add style transfer and artistic filters
// TODO: Implement intelligent upscaling and enhancement
// TODO: Add video content creation capabilities

// IMPROVEMENTS MADE:
// ✅ Direct OpenAI API integration for image generation, editing, and variations
// ✅ Style settings now properly impact the final prompt and image generation
// ✅ Enhanced prompt building with comprehensive style integration
// ✅ Modern DALL-E 3 features including HD quality and native style parameters
// ✅ Visual feedback showing active style settings to users
// ✅ Better error handling and logging for debugging
// ✅ Proper API usage tracking for all image operations
// ✅ API usage limit checking before each direct API call
// ✅ User-friendly error messages for different failure scenarios
// ✅ Enhanced prompt preview showing users exactly how their styles affect prompts
// ✅ Real-time prompt enhancement with expandable preview and copy functionality
// ✅ Detailed breakdown of active style enhancements for transparency
// ✅ Refactored into smaller, reusable components following Flutter best practices

/// Enhanced image generation page for creating AI-generated images
/// Provides comprehensive image generation, editing, and variation capabilities
class GenerateImagePage extends StatefulWidget {
  /// Page title displayed in the app bar
  final String title;

  /// Usage type for tracking and billing purposes
  final Usage type;

  /// Current user ID for authentication and tracking
  final String currentUserId;

  const GenerateImagePage({
    super.key,
    required this.title,
    required this.type,
    required this.currentUserId,
  });

  @override
  State<GenerateImagePage> createState() => _GenerateImagePageState();
}



/// State class for the GenerateImagePage widget
///
/// Manages all state related to image generation, user input, and UI interactions.
/// Follows Flutter best practices for state management and resource disposal.
class _GenerateImagePageState extends State<GenerateImagePage> {
  // MARK: - Core State Variables

  /// Current user input text for image generation
  String _inputText = '';

  /// URL of the most recently generated image
  String _imageUrl = '';

  /// Timestamp of the last image generation request
  DateTime _requestDateTime = DateTime.fromMillisecondsSinceEpoch(0);

  // MARK: - Input Metrics

  /// Current word count in the input text
  int _wordCount = 0;

  /// Current character count in the input text
  int _letterCount = 0;

  // MARK: - Image Handling

  /// Selected source image for editing or variations
  File? _image;

  /// Mask image for selective editing (optional)
  File? _mask;

  /// Image picker instance for selecting images from gallery
  final ImagePicker _picker = ImagePicker();

  // MARK: - UI State

  /// List of chat message widgets to display
  List<Widget> _chatMessages = [];

  /// Currently selected image operation (edit, variations, etc.)
  ImageOperation? _selectedOperation;

  /// Whether an image generation request is in progress
  bool _isLoading = false;

  /// Whether to show the advanced settings panel
  bool _showAdvancedSettings = false;

  /// Whether to show the style presets panel
  bool _showStylePresets = false;

  /// Whether the style presets panel is expanded
  bool _stylePresetsExpanded = true;

  // MARK: - Controllers and Focus Management

  /// Text controller for the input field
  final TextEditingController _textController = TextEditingController();

  /// Stream controller for chat messages updates
  final StreamController<List<Widget>> _chatStreamController =
      StreamController<List<Widget>>.broadcast();

  /// Focus node for better input field handling
  final FocusNode _inputFocusNode = FocusNode();

  // MARK: - Image Generation Settings

  /// Current image generation configuration
  ImageGenerationSettings _generationSettings = const ImageGenerationSettings();

  // MARK: - Computed Properties

  /// Get the usage type from widget for tracking and billing
  Usage get usageType => widget.type;

  // MARK: - Lifecycle Methods

  @override
  void initState() {
    super.initState();
    // Initialize chat stream with empty messages
    _chatStreamController.add(_chatMessages);

    // Add listener to text controller for real-time prompt preview updates
    _textController.addListener(_onTextChanged);
  }

  /// Handles text changes for real-time updates
  void _onTextChanged() {
    setState(() {
      _wordCount = Util_Token.countTokens(_textController.text.trim());
      _letterCount = _textController.text.trim().length;
    });
  }

  @override
  void dispose() {
    // Dispose of controllers and streams to prevent memory leaks
    _chatStreamController.close();
    _textController.dispose();
    _inputFocusNode.dispose();
    super.dispose();
  }

  // MARK: - Image Handling Methods

  /// Picks an image from the gallery for editing or variations
  ///
  /// Allows users to select an image from their device gallery and processes
  /// it for use in image editing or variation generation operations.
  Future<void> _pickImage() async {
    try {
      final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
      if (pickedFile != null) {
        final File? processedImage = await ImageUtils.processImage(
          File(pickedFile.path),
        );
        if (processedImage != null) {
          setState(() {
            _image = processedImage;
          });
          logger.d('Image selected and processed successfully');
        } else {
          _showSnackBar('Invalid image format. Please select a valid image.');
          logger.w('Image processing failed - invalid format');
        }
      } else {
        logger.d('No image selected by user');
      }
    } catch (e) {
      _showSnackBar('Error selecting image. Please try again.');
      logger.e('Error picking image: $e');
    }
  }

  /// Edits the selected image using AI with the provided prompt
  ///
  /// Uses the extracted ImageGenerationService for better separation of concerns.
  Future<void> _editImage() async {
    try {
      await ImageGenerationService.validateEditingRequest(
        _image,
        _textController.text,
        widget.currentUserId,
        usageType,
      );

      setState(() {
        _isLoading = true;
      });

      final editedImageUrl = await ImageGenerationService.editImage(
        _image!,
        _mask,
        _textController.text.trim(),
        _generationSettings,
        widget.currentUserId,
        usageType,
      );

      if (mounted) {
        setState(() {
          _inputText = _textController.text.trim();
          _imageUrl = editedImageUrl;
          _isLoading = false;
          _requestDateTime = DateTime.now().toUtc();
          _updateChatMessages();
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showSnackBar(e.toString());
        logger.e('Error editing image: $e');
      }
    }
  }

  /// Creates variations of the selected image using AI
  ///
  /// Uses the extracted ImageGenerationService for better separation of concerns.
  Future<void> _createVariations() async {
    try {
      await ImageGenerationService.validateVariationRequest(
        _image,
        widget.currentUserId,
        usageType,
      );

      setState(() {
        _isLoading = true;
      });

      final variations = await ImageGenerationService.createVariations(
        _image!,
        widget.currentUserId,
        usageType,
      );

      if (mounted) {
        setState(() {
          _inputText = 'Image variations';
          _imageUrl = variations;
          _isLoading = false;
          _requestDateTime = DateTime.now().toUtc();
          _updateChatMessages();
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showSnackBar(e.toString());
        logger.e('Error creating variations: $e');
      }
    }
  }



  // MARK: - Message Handling Methods

  /// Sends a message to generate a new image using AI
  ///
  /// Uses the extracted ImageGenerationService for better separation of concerns.
  ///
  /// [message] The user's prompt for image generation
  /// [usageType] The type of usage for tracking and billing
  Future<void> _sendMessage(String message, Usage usageType) async {
    try {
      await ImageGenerationService.validateGenerationRequest(
        message,
        widget.currentUserId,
        usageType,
      );

      // Show user feedback
      BotToast.showText(
        text: "Please be patient while image generation is in progress...",
        duration: const Duration(seconds: 3),
      );

      BotToast.showLoading();
      setState(() {
        _isLoading = true;
      });

      final response = await ImageGenerationService.generateImage(
        message.trim(),
        _generationSettings,
        widget.currentUserId,
        usageType,
      );

      if (mounted) {
        setState(() {
          _inputText = message;
          _imageUrl = response;
          _requestDateTime = DateTime.now().toUtc();
          _isLoading = false;
          _updateChatMessages();
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showSnackBar(e.toString());
        logger.e('Image generation failed: $e');
      }
    } finally {
      BotToast.closeAllLoading();
    }
  }

  // MARK: - Chat Message Management

  /// Creates chat message widgets for the current generation result
  ///
  /// Builds a complete chat message including header, body, image, and actions
  /// only if there's a valid generation result to display.
  ///
  /// Returns a list of widgets representing the chat message
  List<Widget> _createChatMessage() {
    return <Widget>[
      if (_requestDateTime != DateTime.fromMillisecondsSinceEpoch(0)) ...[
        Column(
          children: [
            ChatMessageHeader(requestDateTime: _requestDateTime),
            ChatMessageBody(inputText: _inputText),
            ChatMessageImage(imageUrl: _imageUrl),
            ChatMessageActions(
              imageUrl: _imageUrl,
              inputText: _inputText,
              currentUserId: widget.currentUserId,
            ),
          ],
        ),
      ],
    ];
  }

  /// Updates the chat messages list and notifies listeners
  ///
  /// Adds new chat messages to the existing list and updates the stream
  /// to trigger UI rebuilds. Called after successful image generation.
  void _updateChatMessages() {
    setState(() {
      _chatMessages.addAll(_createChatMessage());
    });
    _chatStreamController.sink.add(_chatMessages);
  }

  // MARK: - UI Building Methods

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
      appBar: _buildAppBar(),
      body: _buildBody(context, theme, isDark),
    );
  }

  /// Builds the app bar with navigation actions
  ///
  /// Creates a consistent app bar with history and offline generation options
  /// following the app's design system.
  PreferredSizeWidget _buildAppBar() {
    return AppBarStyles.primary(
      title: 'AI Image Generator',
      actions: [
        // Chat History Button
        IconButton(
          key: const Key("ChatHistoryButtonOnChatPage"),
          icon: const Icon(Icons.history, color: Colors.white),
          tooltip: 'Image Generation History',
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ChatHistoryPage(usage: usageType),
              ),
            );
          },
        ),
        // Offline Generation (iOS/macOS only)
        if (!kIsWeb && (Platform.isIOS || Platform.isMacOS))
          IconButton(
            key: const Key("GenerateImageButtonOfflinePage"),
            icon: const Icon(Icons.offline_bolt_outlined, color: Colors.white),
            tooltip: 'Offline Image Generation',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const OfflineImageGenerationPage(),
                ),
              );
            },
          ),
      ],
    );
  }

  /// Builds the main body of the page
  ///
  /// Organizes the main content including chat area, settings panels,
  /// image thumbnails, and input controls using extracted widgets.
  Widget _buildBody(BuildContext context, ThemeData theme, bool isDark) {
    return Column(
      children: <Widget>[
        // Chat area - shows messages or empty state
        Expanded(
          child: _buildChatArea(context, theme),
        ),

        // Scrollable panels area to prevent overflow
        Flexible(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Style presets panel (when visible)
                ImageStylePresetsPanel(
                  selectedStyleCategory: _generationSettings.styleCategory,
                  selectedStyle: _generationSettings.selectedStyle,
                  onStyleCategoryChanged: (category) {
                    setState(() {
                      _generationSettings = _generationSettings.copyWith(
                        styleCategory: category,
                        selectedStyle: null, // Clear specific style when category changes
                      );
                    });
                  },
                  onStyleChanged: (style) {
                    setState(() {
                      _generationSettings = _generationSettings.copyWith(selectedStyle: style);
                    });
                  },
                  onClose: () {
                    setState(() {
                      _showStylePresets = false;
                    });
                  },
                  onToggleExpanded: () {
                    setState(() {
                      _stylePresetsExpanded = !_stylePresetsExpanded;
                    });
                  },
                  isVisible: _showStylePresets,
                  isExpanded: _stylePresetsExpanded,
                ),

                // Advanced settings panel (when visible)
                ImageGenerationSettingsPanel(
                  settings: _generationSettings,
                  onSettingsChanged: (settings) {
                    setState(() {
                      _generationSettings = settings;
                    });
                  },
                  onClose: () {
                    setState(() {
                      _showAdvancedSettings = false;
                    });
                  },
                  isVisible: _showAdvancedSettings,
                ),

                // Image thumbnails and controls (when images are selected)
                ImageThumbnailsWidget(
                  sourceImage: _image,
                  maskImage: _mask,
                  onClearImages: () {
                    setState(() {
                      _image = null;
                      _mask = null;
                    });
                  },
                  onRemoveSourceImage: () {
                    setState(() {
                      _image = null;
                    });
                  },
                  onRemoveMaskImage: () {
                    setState(() {
                      _mask = null;
                    });
                  },
                ),
              ],
            ),
          ),
        ),

        // Input area with controls (always at bottom)
        ImageGenerationInputArea(
          textController: _textController,
          focusNode: _inputFocusNode,
          wordCount: _wordCount,
          letterCount: _letterCount,
          isLoading: _isLoading,
          settings: _generationSettings,
          sourceImage: _image,
          maskImage: _mask,
          selectedOperation: _selectedOperation,
          onGenerate: () => _sendMessage(_textController.text, usageType),
          onEditImage: _editImage,
          onCreateVariations: _createVariations,
          onPickImage: _pickImage,
          onSettingsChanged: (settings) {
            setState(() {
              _generationSettings = settings;
            });
          },
          onOperationChanged: (operation) {
            setState(() {
              _selectedOperation = operation;
            });
          },
          onToggleStylePresets: () {
            setState(() {
              _showStylePresets = !_showStylePresets;
              // Auto-expand when showing
              if (_showStylePresets) {
                _stylePresetsExpanded = true;
              }
            });
          },
          onToggleAdvancedSettings: () {
            setState(() {
              _showAdvancedSettings = !_showAdvancedSettings;
            });
          },
          showStylePresets: _showStylePresets,
          showAdvancedSettings: _showAdvancedSettings,
        ),
      ],
    );
  }

  /// Builds the chat area showing messages or empty state
  Widget _buildChatArea(BuildContext context, ThemeData theme) {
    if (_chatMessages.isEmpty) {
      return ImageGenerationEmptyState(
        onStartCreating: () {
          _inputFocusNode.requestFocus();
        },
      );
    }

    return StreamBuilder<List<Widget>>(
      stream: _chatStreamController.stream,
      initialData: _chatMessages,
      builder: (BuildContext context, AsyncSnapshot<List<Widget>> snapshot) {
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: snapshot.data?.length ?? 0,
          reverse: true,
          itemBuilder: (BuildContext context, int index) {
            final messages = snapshot.data!;
            return messages[messages.length - 1 - index];
          },
        );
      },
    );
  }

  // MARK: - Utility Methods

  /// Shows a snackbar with the given message
  ///
  /// Provides user feedback for various operations and error states.
  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message)),
      );
    }
  }


















}
