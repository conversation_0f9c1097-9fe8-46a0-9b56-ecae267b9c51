/// Image generation models and enums for the AI image generation feature
///
/// This file contains all the data models, enums, and settings classes
/// used for configuring and managing AI image generation.

/// Available image operations for enhanced functionality
enum ImageOperation {
  /// Edit an existing image with AI
  editImage,

  /// Create variations of an existing image
  createVariations,
}

/// Style categories for image generation
enum StyleCategory {
  photorealistic,
  artistic,
  technical,
  creative,
}

/// Lighting and mood options
enum LightingMood {
  dramatic,
  soft,
  neon,
  vintage,
  natural,
  cinematic,
  golden,
  blue,
}

/// Composition guides
enum CompositionGuide {
  ruleOfThirds,
  goldenRatio,
  centered,
  leadingLines,
  symmetrical,
  asymmetrical,
}

/// Subject-specific templates
enum SubjectTemplate {
  portrait,
  landscape,
  product,
  logo,
  architecture,
  food,
  fashion,
  abstract,
}

/// Advanced image generation settings
///
/// Immutable configuration class for image generation parameters.
/// Follows best practices with const constructor and copyWith method.
class ImageGenerationSettings {
  /// Style category for the image (photorealistic, artistic, etc.)
  final StyleCategory? styleCategory;

  /// Specific style within the selected category
  final String? selectedStyle;

  /// Lighting and mood settings for the image
  final LightingMood? lightingMood;

  /// Composition guide for image layout
  final CompositionGuide? compositionGuide;

  /// Subject template for focused generation
  final SubjectTemplate? subjectTemplate;

  /// Aspect ratio for the generated image
  final double aspectRatio;

  /// Image size in pixels
  final int imageSize;

  /// Whether to use AI-enhanced prompting
  final bool useAdvancedPrompting;

  /// Negative prompt to avoid certain elements
  final String negativePrompt;

  const ImageGenerationSettings({
    this.styleCategory,
    this.selectedStyle,
    this.lightingMood,
    this.compositionGuide,
    this.subjectTemplate,
    this.aspectRatio = 1.0,
    this.imageSize = 1024,
    this.useAdvancedPrompting = false,
    this.negativePrompt = '',
  });

  /// Creates a copy of this settings object with the given fields replaced
  /// with new values. Follows Flutter best practices for immutable objects.
  ImageGenerationSettings copyWith({
    StyleCategory? styleCategory,
    String? selectedStyle,
    LightingMood? lightingMood,
    CompositionGuide? compositionGuide,
    SubjectTemplate? subjectTemplate,
    double? aspectRatio,
    int? imageSize,
    bool? useAdvancedPrompting,
    String? negativePrompt,
  }) {
    return ImageGenerationSettings(
      styleCategory: styleCategory ?? this.styleCategory,
      selectedStyle: selectedStyle ?? this.selectedStyle,
      lightingMood: lightingMood ?? this.lightingMood,
      compositionGuide: compositionGuide ?? this.compositionGuide,
      subjectTemplate: subjectTemplate ?? this.subjectTemplate,
      aspectRatio: aspectRatio ?? this.aspectRatio,
      imageSize: imageSize ?? this.imageSize,
      useAdvancedPrompting: useAdvancedPrompting ?? this.useAdvancedPrompting,
      negativePrompt: negativePrompt ?? this.negativePrompt,
    );
  }

  /// Checks if any style settings are configured
  bool get hasStyleSettings {
    return styleCategory != null ||
        selectedStyle != null ||
        lightingMood != null ||
        compositionGuide != null ||
        subjectTemplate != null;
  }

  /// Gets a human-readable description of active settings
  String get activeSettingsDescription {
    final List<String> descriptions = [];

    if (selectedStyle != null && selectedStyle!.isNotEmpty) {
      descriptions.add('Style: $selectedStyle');
    } else if (styleCategory != null) {
      descriptions.add('Category: ${_formatEnumName(styleCategory.toString())}');
    }

    if (lightingMood != null) {
      descriptions.add('Lighting: ${_formatEnumName(lightingMood.toString())}');
    }

    if (compositionGuide != null) {
      descriptions.add('Composition: ${_formatEnumName(compositionGuide.toString())}');
    }

    if (subjectTemplate != null) {
      descriptions.add('Subject: ${_formatEnumName(subjectTemplate.toString())}');
    }

    if (useAdvancedPrompting) {
      descriptions.add('Enhanced prompting enabled');
    }

    return descriptions.isEmpty ? 'No style settings' : descriptions.join(', ');
  }

  /// Formats enum names for display
  String _formatEnumName(String enumValue) {
    final name = enumValue.split('.').last;
    return name.replaceAllMapped(
      RegExp(r'([A-Z])'),
      (match) => ' ${match.group(1)}',
    ).trim();
  }

  @override
  String toString() {
    return 'ImageGenerationSettings('
        'styleCategory: $styleCategory, '
        'selectedStyle: $selectedStyle, '
        'lightingMood: $lightingMood, '
        'compositionGuide: $compositionGuide, '
        'subjectTemplate: $subjectTemplate, '
        'aspectRatio: $aspectRatio, '
        'imageSize: $imageSize, '
        'useAdvancedPrompting: $useAdvancedPrompting, '
        'negativePrompt: $negativePrompt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ImageGenerationSettings &&
        other.styleCategory == styleCategory &&
        other.selectedStyle == selectedStyle &&
        other.lightingMood == lightingMood &&
        other.compositionGuide == compositionGuide &&
        other.subjectTemplate == subjectTemplate &&
        other.aspectRatio == aspectRatio &&
        other.imageSize == imageSize &&
        other.useAdvancedPrompting == useAdvancedPrompting &&
        other.negativePrompt == negativePrompt;
  }

  @override
  int get hashCode {
    return Object.hash(
      styleCategory,
      selectedStyle,
      lightingMood,
      compositionGuide,
      subjectTemplate,
      aspectRatio,
      imageSize,
      useAdvancedPrompting,
      negativePrompt,
    );
  }
}
