import 'dart:io';

import 'package:flutter/material.dart';

import 'package:diogeneschatbot/features/image_generation/models/image_generation_settings.dart';
import 'package:diogeneschatbot/features/image_generation/services/image_generation_service.dart';
import 'package:diogeneschatbot/features/image_generation/widgets/image_style_presets_panel.dart';
import 'package:diogeneschatbot/features/image_generation/widgets/image_thumbnails_widget.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';

/// Input area widget for image generation
///
/// Provides the main input interface including text field, action buttons,
/// prompt preview, and quick style selection controls.
class ImageGenerationInputArea extends StatelessWidget {
  /// Text controller for the input field
  final TextEditingController textController;

  /// Focus node for the input field
  final FocusNode focusNode;

  /// Current word count in the input
  final int wordCount;

  /// Current character count in the input
  final int letterCount;

  /// Whether a generation request is in progress
  final bool isLoading;

  /// Current image generation settings
  final ImageGenerationSettings settings;

  /// Selected source image for editing/variations
  final File? sourceImage;

  /// Selected mask image for editing
  final File? maskImage;

  /// Currently selected image operation
  final ImageOperation? selectedOperation;

  /// Callback when generate button is pressed
  final VoidCallback? onGenerate;

  /// Callback when edit image button is pressed
  final VoidCallback? onEditImage;

  /// Callback when create variations button is pressed
  final VoidCallback? onCreateVariations;

  /// Callback when pick image button is pressed
  final VoidCallback? onPickImage;

  /// Callback when settings change
  final ValueChanged<ImageGenerationSettings>? onSettingsChanged;

  /// Callback when operation selection changes
  final ValueChanged<ImageOperation?>? onOperationChanged;

  /// Callback to toggle style presets panel
  final VoidCallback? onToggleStylePresets;

  /// Callback to toggle advanced settings panel
  final VoidCallback? onToggleAdvancedSettings;

  /// Whether style presets panel is visible
  final bool showStylePresets;

  /// Whether advanced settings panel is visible
  final bool showAdvancedSettings;

  const ImageGenerationInputArea({
    super.key,
    required this.textController,
    required this.focusNode,
    required this.wordCount,
    required this.letterCount,
    required this.isLoading,
    required this.settings,
    this.sourceImage,
    this.maskImage,
    this.selectedOperation,
    this.onGenerate,
    this.onEditImage,
    this.onCreateVariations,
    this.onPickImage,
    this.onSettingsChanged,
    this.onOperationChanged,
    this.onToggleStylePresets,
    this.onToggleAdvancedSettings,
    this.showStylePresets = false,
    this.showAdvancedSettings = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
        border: Border(
          top: BorderSide(
            color: isDark ? AppTheme.darkOutline : AppTheme.lightOutline,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Prompt preview section
              if (textController.text.isNotEmpty) _buildPromptPreview(theme),

              // Style preview chip
              if (settings.hasStyleSettings) ...[
                const SizedBox(height: 8),
                StylePreviewChip(
                  settings: settings,
                  onClear: () => onSettingsChanged?.call(const ImageGenerationSettings()),
                ),
              ],

              const SizedBox(height: 8),

              // Main input field
              _buildInputField(theme),

              const SizedBox(height: 8),

              // Input metrics and controls
              _buildInputMetrics(theme),

              const SizedBox(height: 12),

              // Action buttons row
              _buildActionButtons(theme),

              const SizedBox(height: 8),

              // Image operation buttons (when image is selected)
              if (sourceImage != null) _buildImageOperationButtons(theme),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the enhanced prompt preview section
  Widget _buildPromptPreview(ThemeData theme) {
    final enhancedPrompt = ImageGenerationService.getEnhancedPromptPreview(
      textController.text,
      settings,
    );

    if (enhancedPrompt == textController.text) {
      return const SizedBox.shrink();
    }

    return CardStyles.outlined(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.auto_awesome, size: 16, color: AppTheme.primaryGreen),
              const SizedBox(width: 4),
              Text(
                'Enhanced Prompt Preview',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryGreen,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.copy, size: 16),
                onPressed: () {
                  // TODO: Copy enhanced prompt to clipboard
                },
                tooltip: 'Copy enhanced prompt',
                style: IconButton.styleFrom(
                  padding: const EdgeInsets.all(4),
                  minimumSize: const Size(24, 24),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            enhancedPrompt,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.hintColor,
              fontStyle: FontStyle.italic,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// Builds the main input text field
  Widget _buildInputField(ThemeData theme) {
    return TextField(
      controller: textController,
      focusNode: focusNode,
      decoration: InputDecoration(
        hintText: 'Describe the image you want to generate...',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppTheme.primaryGreen, width: 2),
        ),
        contentPadding: const EdgeInsets.all(16),
        suffixIcon: isLoading
            ? const Padding(
                padding: EdgeInsets.all(12),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            : null,
      ),
      maxLines: 3,
      textInputAction: TextInputAction.newline,
      enabled: !isLoading,
    );
  }

  /// Builds the input metrics and toggle buttons
  Widget _buildInputMetrics(ThemeData theme) {
    return Row(
      children: [
        // Word and character count
        Text(
          '$wordCount words • $letterCount characters',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.hintColor,
          ),
        ),
        const Spacer(),

        // Style presets toggle
        IconButton(
          icon: Icon(
            showStylePresets ? Icons.palette : Icons.palette_outlined,
            color: showStylePresets ? AppTheme.primaryGreen : theme.hintColor,
          ),
          onPressed: onToggleStylePresets,
          tooltip: 'Style Presets',
        ),

        // Advanced settings toggle
        IconButton(
          icon: Icon(
            showAdvancedSettings ? Icons.tune : Icons.tune_outlined,
            color: showAdvancedSettings ? AppTheme.primaryGreen : theme.hintColor,
          ),
          onPressed: onToggleAdvancedSettings,
          tooltip: 'Advanced Settings',
        ),
      ],
    );
  }

  /// Builds the main action buttons
  Widget _buildActionButtons(ThemeData theme) {
    return Row(
      children: [
        // Pick image button
        ImageSelectionButton(
          icon: Icons.image,
          label: 'Pick Image',
          tooltip: 'Select image for editing or variations',
          onPressed: onPickImage,
          enabled: !isLoading,
        ),

        const SizedBox(width: 8),

        // Generate button
        Expanded(
          child: ElevatedButton.icon(
            onPressed: isLoading ? null : onGenerate,
            icon: isLoading
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.auto_awesome, color: Colors.white),
            label: Text(
              isLoading ? 'Generating...' : 'Generate Image',
              style: const TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryGreen,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds image operation buttons when an image is selected
  Widget _buildImageOperationButtons(ThemeData theme) {
    return Column(
      children: [
        ImageOperationButton(
          icon: Icons.edit,
          label: 'Edit Image',
          description: 'Modify the selected image with AI',
          onPressed: isLoading ? null : onEditImage,
          enabled: !isLoading && textController.text.trim().isNotEmpty,
        ),
        ImageOperationButton(
          icon: Icons.auto_fix_high,
          label: 'Create Variations',
          description: 'Generate variations of the selected image',
          onPressed: isLoading ? null : onCreateVariations,
          enabled: !isLoading,
        ),
      ],
    );
  }
}

/// Compact input area for smaller screens or embedded use
class CompactImageGenerationInput extends StatelessWidget {
  /// Text controller for the input field
  final TextEditingController textController;

  /// Current image generation settings
  final ImageGenerationSettings settings;

  /// Whether a generation request is in progress
  final bool isLoading;

  /// Callback when generate button is pressed
  final VoidCallback? onGenerate;

  /// Callback when settings change
  final ValueChanged<ImageGenerationSettings>? onSettingsChanged;

  const CompactImageGenerationInput({
    super.key,
    required this.textController,
    required this.settings,
    required this.isLoading,
    this.onGenerate,
    this.onSettingsChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Compact style selector
        CompactStyleSelector(
          selectedStyleCategory: settings.styleCategory,
          selectedStyle: settings.selectedStyle,
          onStyleCategoryChanged: (category) {
            onSettingsChanged?.call(settings.copyWith(styleCategory: category));
          },
          onStyleChanged: (style) {
            onSettingsChanged?.call(settings.copyWith(selectedStyle: style));
          },
        ),

        const SizedBox(height: 8),

        // Input field with generate button
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: textController,
                decoration: InputDecoration(
                  hintText: 'Describe your image...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.all(12),
                ),
                enabled: !isLoading,
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: isLoading ? null : onGenerate,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryGreen,
                padding: const EdgeInsets.all(12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.auto_awesome, color: Colors.white),
            ),
          ],
        ),
      ],
    );
  }
}
